import pandas as pd

def find_extreme_values(csv_file, column_name, num_extremes=10):
    """
    读取CSV文件并找出指定列中最大和最小的10个数字
    
    参数:
        csv_file (str): CSV文件路径
        column_name (str): 要分析的列名
        num_extremes (int): 要查找的最大/最小值数量(默认为10)
        
    返回:
        dict: 包含最大和最小值的字典
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        
        # 检查列是否存在
        if column_name not in df.columns:
            raise ValueError(f"列 '{column_name}' 不存在于CSV文件中")
            
        # 转换为数值类型(自动处理非数值数据)
        series = pd.to_numeric(df[column_name], errors='coerce')
        
        # 删除NaN值
        clean_series = series.dropna()
        
        if len(clean_series) == 0:
            raise ValueError("该列没有有效的数值数据")
            
        # 获取最大的10个值
        top_values = clean_series.nlargest(num_extremes)
        
        # 获取最小的10个值
        bottom_values = clean_series.nsmallest(num_extremes)
        
        return {
            'top_values': top_values.to_dict(),
            'bottom_values': bottom_values.to_dict(),
            'column': column_name,
            'file': csv_file
        }
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

# 使用示例
if __name__ == "__main__":
    # 替换为你的CSV文件路径和列名
    file_path = "cb_factors2.csv"  
    column_to_analyze = "open"  
    
    results = find_extreme_values(file_path, column_to_analyze)
    
    if results:
        print(f"\n文件 '{results['file']}' 中列 '{results['column']}' 的分析结果:")
        
        print("\n最大的10个值:")
        for rank, (index, value) in enumerate(results['top_values'].items(), 1):
            print(f"{rank}. 行 {index}: {value}")
            
        print("\n最小的10个值:")
        for rank, (index, value) in enumerate(results['bottom_values'].items(), 1):
            print(f"{rank}. 行 {index}: {value}")

import pandas as pd

def filter_csv_by_column_value(input_file, output_file, column_name, threshold):
    """
    删除指定列中数值小于阈值的所有行
    
    参数:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径
        column_name (str): 要过滤的列名
        threshold (float): 阈值，小于此值的行将被删除
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        
        print(f"原始数据行数: {len(df)}")
        
        # 检查列是否存在
        if column_name not in df.columns:
            raise ValueError(f"列 '{column_name}' 不存在于CSV文件中")
            
        # 将指定列转换为数值类型(非数值转为NaN)
        df[column_name] = pd.to_numeric(df[column_name], errors='coerce')
        
        # 删除小于阈值的行
        filtered_df = df[df[column_name] >= threshold]
        
        # 删除转换过程中产生的NaN行(可选)
        filtered_df = filtered_df.dropna(subset=[column_name])
        
        print(f"过滤后数据行数: {len(filtered_df)}")
        print(f"删除了 {len(df) - len(filtered_df)} 行")
        
        # 保存到新文件
        filtered_df.to_csv(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")

# 使用示例
if __name__ == "__main__":
    # 替换为你的文件路径和参数
    input_csv = "cb_factors2.csv"      # 输入文件
    output_csv = "cb_factors3.csv"    # 输出文件
    column = "open"            # 要过滤的列名
    min_value = 1             # 阈值
    
    filter_csv_by_column_value(input_csv, output_csv, column, min_value)

import pandas as pd

def remove_rows_with_empty_columns(input_file, output_file, columns_to_check):
    """
    删除指定列中为空值的整行
    
    参数:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
        columns_to_check: 要检查的列名列表或单个列名
    """
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    # 如果传入的是单个列名，转换为列表
    if isinstance(columns_to_check, str):
        columns_to_check = [columns_to_check]
    
    # 删除指定列中任何一列为空值的行
    df_cleaned = df.dropna(subset=columns_to_check)
    
    # 保存处理后的数据
    df_cleaned.to_csv(output_file, index=False)
    print(f"处理完成，结果已保存到 {output_file}")
    print(f"原始行数: {len(df)}，处理后行数: {len(df_cleaned)}")

# 使用示例
if __name__ == "__main__":
    input_csv = "cb_factors.csv"      # 输入文件路径
    output_csv = "cb_factors1.csv"    # 输出文件路径
    columns = "cb_momentum_60d"  # 要检查的列名列表
    
    # 也可以只检查单个列
    # columns = "column1"
    # 多个列
    #["column1", "column2"] 
    remove_rows_with_empty_columns(input_csv, output_csv, columns)

import pandas as pd

def fill_empty_with_zero(input_file, output_file, column_name):
    """
    将CSV文件中指定列的缺失值补0
    
    参数:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
        column_name: 要处理的列名
    """
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    # 统计补0前的空值数量
    null_count_before = df[column_name].isnull().sum()
    
    # 将指定列的空值替换为0
    df[column_name] = df[column_name].fillna(0)
    
    # 保存处理后的数据
    df.to_csv(output_file, index=False)
    
    # 输出处理结果
    print(f"处理完成，结果已保存到 {output_file}")
    print(f"列 '{column_name}' 中共有 {null_count_before} 个空值被替换为0")

# 使用示例
if __name__ == "__main__":
    input_csv = "cb_factors3.csv"      # 输入文件路径
    output_csv = "cb_factors4.csv"    # 输出文件路径
    column = "cb_implied_volatility"  # 要处理的列名
    
    fill_empty_with_zero(input_csv, output_csv, column)

import pandas as pd
import numpy as np

def analyze_data(csv_file):
    """
    检查CSV文件中所有列的缺失值和inf值，并报告trade_date列中最新的日期
    
    参数:
        csv_file: 要检查的CSV文件路径
    """
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    
    # 1. 检查缺失值
    missing_data = {}
    for column in df.columns:
        null_rows = df[df[column].isnull()].index.tolist()
        if null_rows:
            missing_data[column] = [row + 1 for row in null_rows]  # +1因为索引从0开始
    
    # 2. 检查inf值
    inf_data = {}
    for column in df.select_dtypes(include=[np.number]).columns:  # 只检查数值列
        inf_rows = df[np.isinf(df[column])].index.tolist()
        if inf_rows:
            inf_data[column] = [row + 1 for row in inf_rows]
    
    # 3. 检查trade_date列中最新的日期
    latest_date = None
    if 'trade_date' in df.columns:
        try:
            # 尝试转换为日期格式
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            latest_date = df['trade_date'].max()
        except:
            # 如果转换失败，直接取最大值
            latest_date = df['trade_date'].max()
    oldest_date = None
    if 'trade_date' in df.columns:
        try:
            # 尝试转换为日期格式
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            oldest_date = df['trade_date'].min()
        except:
            # 如果转换失败，直接取最大值
            oldest_date = df['trade_date'].min()
    # 输出结果
    print("="*50)
    print("数据分析报告")
    print("="*50)
    
    # 缺失值报告
    if not missing_data:
        print("\n1. 文件中没有缺失值")
    else:
        print("\n1. 发现以下缺失值:")
        for column, rows in missing_data.items():
            print(f"\n列名: {column}")
            print(f"缺失行数: {len(rows)}")
            print(f"具体行号: {rows}")
    
    # inf值报告
    if not inf_data:
        print("\n2. 文件中没有inf值")
    else:
        print("\n2. 发现以下inf值:")
        for column, rows in inf_data.items():
            print(f"\n列名: {column}")
            print(f"包含inf的行数: {len(rows)}")
            print(f"具体行号: {rows}")
    
    # 最新日期报告
    if latest_date is not None:
        print(f"\n3. trade_date列中最新的日期是: {latest_date}")
    else:
        print("\n3. 文件中没有trade_date列")
    if oldest_date is not None:
        print(f"\n3. trade_date列中最新的日期是: {oldest_date}")
    else:
        print("\n3. 文件中没有trade_date列")

if __name__ == "__main__":
    file_path = "cb_factors.csv"
    analyze_data(file_path)

import pandas as pd

def remove_rows_with_any_missing(input_file, output_file):
    """
    删除CSV文件中任何列包含缺失值的整行
    
    参数:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
    """
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    # 记录原始行数
    original_rows = len(df)
    
    # 删除任何列包含缺失值的行
    df_cleaned = df.dropna(how='any')
    
    # 保存处理后的数据
    df_cleaned.to_csv(output_file, index=False)
    
    # 计算删除的行数
    removed_rows = original_rows - len(df_cleaned)
    
    # 输出结果
    print(f"处理完成，结果已保存到: {output_file}")
    print(f"原始行数: {original_rows}")
    print(f"删除行数: {removed_rows}")
    print(f"保留行数: {len(df_cleaned)}")
    print(f"删除的行占总行数的: {removed_rows/original_rows:.2%}")

# 使用示例
if __name__ == "__main__":
    input_csv = "cb_factors5.csv"      # 输入文件路径
    output_csv = "cb_factors6.csv"    # 输出文件路径
    
    remove_rows_with_any_missing(input_csv, output_csv)

import pandas as pd
import numpy as np

def find_and_remove_inf(csv_file, output_file):
    """
    查找并删除包含inf/-inf值的行
    
    参数:
        csv_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
    """
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    
    # 记录原始行数
    original_rows = len(df)
    
    # 查找包含inf/-inf的行
    inf_mask = df.isin([np.inf, -np.inf]).any(axis=1)
    inf_rows = df[inf_mask].index.tolist()
    
    if not inf_rows:
        print("文件中未发现包含inf/-inf值的行")
        df.to_csv(output_file, index=False)
        return
    
    # 显示检测结果
    print(f"发现 {len(inf_rows)} 行包含inf/-inf值")
    
    # 删除这些行
    df_cleaned = df[~inf_mask]
    
    # 保存处理后的数据
    df_cleaned.to_csv(output_file, index=False)
    
    # 输出统计信息
    print(f"\n处理完成，结果已保存到: {output_file}")
    print(f"原始行数: {original_rows}")
    print(f"删除行数: {len(inf_rows)}")
    print(f"保留行数: {len(df_cleaned)}")
    print(f"删除比例: {len(inf_rows)/original_rows:.2%}")

if __name__ == "__main__":
    input_file = "cb_factors5.csv"    # 输入文件路径
    output_file = "cb_factors6.csv"  # 输出文件路径
    
    find_and_remove_inf(input_file, output_file)

import pandas as pd
import numpy as np

def clean_csv_except_column(input_file, output_file, protected_column):
    """
    删除除指定列外的所有inf/-inf/空值所在行
    
    参数:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
        protected_column: 要保护的列名（该列的inf/空值不会被删除）
    """
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    # 检查保护列是否存在
    if protected_column not in df.columns:
        print(f"错误：文件中不存在列 '{protected_column}'")
        return
    
    # 记录原始行数
    original_rows = len(df)
    
    # 1. 找出除保护列外的其他列
    other_columns = [col for col in df.columns if col != protected_column]
    
    # 2. 检查这些列中的inf/-inf/空值
    # 创建掩码：标记出其他列中有问题的行
    mask = (
        df[other_columns].isin([np.inf, -np.inf, np.nan, '', ' '])
        .any(axis=1)
    )
    
    # 3. 删除这些行
    df_cleaned = df[~mask]
    
    # 保存处理后的数据
    df_cleaned.to_csv(output_file, index=False)
    
    # 输出统计信息
    removed_rows = original_rows - len(df_cleaned)
    print(f"处理完成，结果已保存到: {output_file}")
    print(f"原始行数: {original_rows}")
    print(f"删除行数: {removed_rows} (因其他列存在inf/空值)")
    print(f"保留行数: {len(df_cleaned)}")
    print(f"删除比例: {removed_rows/original_rows:.2%}")
    print(f"注意：列 '{protected_column}' 中的inf/空值会被保留")

if __name__ == "__main__":
    input_csv = "cb_factors.csv"       # 输入文件路径
    output_csv = "cb_factors2.csv"    # 输出文件路径
    protected_col = "trade_date"          # 要保护的列名（该列的inf/空值不删除）
    
    clean_csv_except_column(input_csv, output_csv, protected_col)

import pandas as pd

def show_column_names(file_path):
    """
    显示CSV文件的所有列名，按照Python数组格式打印，每行10个
    
    参数:
        file_path (str): CSV文件路径
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        columns = df.columns.tolist()
        
        # 打印Python数组格式的列名
        print("CSV文件中的列名:")
        print("[")
        
        # 每行打印10个元素
        for i in range(0, len(columns), 10):
            row = columns[i:i+10]
            # 处理每行的最后一个元素是否有逗号
            row_str = ", ".join(f"'{col}'" for col in row)
            if i + 10 < len(columns):  # 不是最后一行就加逗号
                row_str += ","
            print(f"    {row_str}")
            
        print("]")
        
        # 打印列名总数
        print(f"\n一共有 {len(columns)} 个列名")
        
        return columns
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 未找到")
        return []
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return []

# 使用示例
if __name__ == "__main__":
    file_path = 'tushare_data_cyb/stock_factors_cyb_train.csv'
    show_column_names(file_path)

import pandas as pd

# 读取CSV文件
file_path = 'tushare_data\stock_factors_train.csv'  # 替换为你的CSV文件路径
data = pd.read_csv(file_path)

# 打印前10行
print("CSV文件的前10行：")
print(data.head(10))

import pandas as pd

def remove_columns(file_path, columns_to_remove, output_file=None):
    """
    从CSV文件中删除指定的列
    
    参数:
        file_path (str): 输入CSV文件路径
        columns_to_remove (list): 要删除的列名列表
        output_file (str): 输出文件路径(可选)，如果为None则覆盖原文件
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        
        # 检查要删除的列是否存在
        existing_columns = set(df.columns)
        columns_to_remove = [col for col in columns_to_remove if col in existing_columns]
        
        if not columns_to_remove:
            print("没有找到要删除的列")
            return
            
        # 删除指定列
        df = df.drop(columns=columns_to_remove)
        
        # 保存结果
        output_path = output_file if output_file else file_path
        df.to_csv(output_path, index=False)
        
        print(f"已成功删除列: {', '.join(columns_to_remove)}")
        print(f"结果已保存到: {output_path}")
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 未找到")
    except Exception as e:
        print(f"发生错误: {str(e)}")

# 使用示例
if __name__ == "__main__":
    file_path = 'cb_factors_train.csv'
    
    to_remove = input("请输入要删除的列名(多个列用逗号分隔): ").strip().split(',')
    to_remove = [col.strip() for col in to_remove if col.strip()]
        
    if to_remove:
        output_file = input("请输入输出文件路径(留空则覆盖原文件): ").strip() or None
        remove_columns(file_path, to_remove, output_file)

import pandas as pd
from datetime import datetime, timedelta

def remove_old_data(input_file, output_file, date_column):
    """
    读取CSV文件，删除两年前的数据，并保存到新文件
    
    参数:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径
        date_column (str): 包含日期的列名
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        
        # 确保日期列存在
        if date_column not in df.columns:
            raise ValueError(f"列 '{date_column}' 不存在于CSV文件中")
            
        # 将日期列转换为datetime类型
        df[date_column] = pd.to_datetime(df[date_column])
        
        # 计算两年前的日期
        two_years_ago = datetime.now() - timedelta(days=400)  # 大约2年
        
        # 筛选出两年内的数据
        filtered_df = df[df[date_column] >= two_years_ago]
        
        # 保存到新文件
        filtered_df.to_csv(output_file, index=False)
        
        print(f"处理完成。已删除两年前的数据，结果保存到 {output_file}")
        print(f"原始数据行数: {len(df)}")
        print(f"处理后行数: {len(filtered_df)}")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")

# 使用示例
if __name__ == "__main__":
    input_csv = "cb_factors.csv"      # 输入CSV文件路径
    output_csv = "cb_factors_2.csv"    # 输出CSV文件路径
    date_col = "trade_date"            # 包含日期的列名
    
    remove_old_data(input_csv, output_csv, date_col)

import pandas as pd
from datetime import datetime

def remove_data_after_date(input_file, output_file, date_column, cutoff_date):
    """
    读取CSV文件，删除指定日期之后的所有数据，并保存到新文件
    
    参数:
        input_file (str): 输入CSV文件路径
        output_file (str): 输出CSV文件路径
        date_column (str): 包含日期的列名
        cutoff_date (str/datetime): 截止日期(格式如'2023-01-01'或datetime对象)
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        
        # 确保日期列存在
        if date_column not in df.columns:
            raise ValueError(f"列 '{date_column}' 不存在于CSV文件中")
            
        # 将日期列转换为datetime类型
        df[date_column] = pd.to_datetime(df[date_column])
        
        # 将截止日期转换为datetime类型
        if not isinstance(cutoff_date, datetime):
            cutoff_date = pd.to_datetime(cutoff_date)
        
        # 筛选出截止日期之前的数据
        filtered_df = df[df[date_column] <= cutoff_date]
        
        # 保存到新文件
        filtered_df.to_csv(output_file, index=False)
        
        print(f"处理完成。已删除 {cutoff_date.date()} 之后的数据，结果保存到 {output_file}")
        print(f"原始数据行数: {len(df)}")
        print(f"处理后行数: {len(filtered_df)}")
        print(f"删除的行数: {len(df) - len(filtered_df)}")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")

# 使用示例
if __name__ == "__main__":
    input_csv = "cb_factors.csv"      # 输入CSV文件路径
    output_csv = "cb_factors_filtered.csv"    # 输出CSV文件路径
    date_col = "trade_date"            # 包含日期的列名
    cutoff_date = "2023-01-01"         # 截止日期
    
    remove_data_after_date(input_csv, output_csv, date_col, cutoff_date)