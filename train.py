import os
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, r2_score
import json
import pickle
from tqdm import tqdm
import shutil

# 导入自定义模块
import config
from data_loader import DataProcessor
from models.samba_model import SAMBAModel
from utils import setup_seed, plot_training_curves

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='SAMBA模型训练')
    parser.add_argument('--factors', nargs='+', default=None, help='要使用的因子列表')
    parser.add_argument('--data_path', type=str, default=config.DATA_PATH, help='CSV数据文件路径')
    parser.add_argument('--epochs', type=int, default=config.EPOCHS, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=config.BATCH_SIZE, help='批大小')
    parser.add_argument('--lr', type=float, default=config.LEARNING_RATE, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=config.WEIGHT_DECAY, help='权重衰减')
    parser.add_argument('--save_interval', type=int, default=config.SAVE_INTERVAL, help='保存间隔(轮)')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--scheduler', type=str, default='plateau',
                       choices=['plateau', 'cosine', 'cosine_warm'],
                       help='学习率调度器类型: plateau(默认), cosine(余弦退火), cosine_warm(带热重启的余弦退火)')
    parser.add_argument('--min_lr', type=float, default=1e-6, help='最小学习率(用于余弦退火)')
    parser.add_argument('--t_max', type=int, default=None, help='余弦退火周期长度(默认为总epochs)')
    parser.add_argument('--t_0', type=int, default=10, help='热重启初始周期长度')
    parser.add_argument('--t_mult', type=int, default=2, help='热重启周期倍数')
    parser.add_argument('--start_date', type=str, default=None, help='训练集开始日期(YYYY-MM-DD)，不指定则使用数据中的最早日期')
    parser.add_argument('--end_date', type=str, default=None, help='训练集截止日期(YYYY-MM-DD)，不指定则使用数据中的最新日期')
    return parser.parse_args()


def create_scheduler(optimizer, scheduler_type, args):
    """
    创建学习率调度器

    参数:
        optimizer: 优化器
        scheduler_type: 调度器类型 ('plateau', 'cosine', 'cosine_warm')
        args: 命令行参数

    返回:
        scheduler: 学习率调度器
        need_step_per_epoch: 是否需要每个epoch调用step()
    """
    if scheduler_type == 'plateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.3,
            patience=10,
            verbose=True
        )
        return scheduler, False  # ReduceLROnPlateau需要传入metric

    elif scheduler_type == 'cosine':
        # 余弦退火调度器
        t_max = args.t_max if args.t_max else args.epochs
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=t_max,
            eta_min=args.min_lr,
            verbose=True
        )
        return scheduler, True  # 每个epoch调用step()

    elif scheduler_type == 'cosine_warm':
        # 带热重启的余弦退火调度器
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=args.t_0,
            T_mult=args.t_mult,
            eta_min=args.min_lr,
            verbose=True
        )
        return scheduler, True  # 每个epoch调用step()

    else:
        raise ValueError(f"不支持的调度器类型: {scheduler_type}")


def train_epoch(model, train_loader, criterion, optimizer, device):
    """
    训练一个epoch
    
    参数:
        model: 模型
        train_loader: 训练数据加载器
        criterion: 损失函数
        optimizer: 优化器
        device: 设备
        
    返回:
        avg_loss: 平均损失
    """
    model.train()
    total_loss = 0
    
    for features, targets in tqdm(train_loader, desc="训练中"):
        features, targets = features.to(device), targets.to(device)
        
        # 前向传播
        outputs = model(features)
        loss = criterion(outputs, targets)
        
        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        # --- 新增：梯度裁剪 ---
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
        # ---------------------
        optimizer.step()
        
        total_loss += loss.item() * features.size(0)
    
    avg_loss = total_loss / len(train_loader.dataset)
    return avg_loss

def validate(model, val_loader, criterion, device):
    """
    在验证集上评估模型
    
    参数:
        model: 模型
        val_loader: 验证数据加载器
        criterion: 损失函数
        device: 设备
        
    返回:
        avg_loss: 平均损失
        metrics: 评估指标字典
    """
    model.eval()
    total_loss = 0
    all_targets = []
    all_preds = []
    
    with torch.no_grad():
        for features, targets in tqdm(val_loader, desc="验证中"):
            features, targets = features.to(device), targets.to(device)
            
            # 前向传播
            outputs = model(features)
            loss = criterion(outputs, targets)
            
            total_loss += loss.item() * features.size(0)
            all_targets.extend(targets.cpu().numpy())
            all_preds.extend(outputs.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader.dataset)
    
    # 计算评估指标
    all_targets = np.array(all_targets)
    all_preds = np.array(all_preds)
    rmse = np.sqrt(mean_squared_error(all_targets, all_preds))
    r2 = r2_score(all_targets, all_preds)
    
    # 计算IC和RIC (按论文指标)
    ic = np.corrcoef(all_preds, all_targets)[0, 1]
    
    # 计算RIC (Rank IC)
    ranks_pred = pd.Series(all_preds).rank()
    ranks_target = pd.Series(all_targets).rank()
    ric = ranks_pred.corr(ranks_target, method='spearman')
    
    metrics = {
        'rmse': rmse,
        'r2': r2,
        'ic': ic,
        'ric': ric
    }
    
    return avg_loss, metrics

def save_checkpoint(model, optimizer, epoch, val_metrics, best_val_loss, best_ric, best_ic, is_best, is_best_ric, is_best_ic, run_id):
    """
    保存检查点

    参数:
        model: 模型
        optimizer: 优化器
        epoch: 当前轮数
        val_metrics: 验证指标
        best_val_loss: 最佳验证损失
        best_ric: 最佳RIC值
        best_ic: 最佳IC值
        is_best: 是否为最佳损失模型
        is_best_ric: 是否为最佳RIC模型
        is_best_ic: 是否为最佳IC模型
        run_id: 运行ID
    """
    # 构建保存路径
    checkpoint_dir = os.path.join(config.OUTPUT_DIR, run_id)
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    # 准备保存数据
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'val_metrics': val_metrics,
        'best_val_loss': best_val_loss,
        'best_ric': best_ric,
        'best_ic': best_ic
    }
    
    # 每N轮保存一次权重，并删除前N轮的权重
    if epoch % config.SAVE_INTERVAL == 0:
        checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch}.pt')
        torch.save(checkpoint, checkpoint_path)
        print(f"已保存检查点至 {checkpoint_path}")
        
        # 删除前N轮的检查点
        old_checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch - config.SAVE_INTERVAL}.pt')
        if os.path.exists(old_checkpoint_path):
            os.remove(old_checkpoint_path)
            print(f"已删除旧检查点 {old_checkpoint_path}")
    
    # 保存最佳损失模型
    if is_best:
        best_model_path = os.path.join(checkpoint_dir, 'best_model.pt')
        torch.save(checkpoint, best_model_path)
        print(f"已保存最佳损失模型至 {best_model_path}")
    
    # 保存最佳RIC模型
    if is_best_ric:
        best_ric_model_path = os.path.join(checkpoint_dir, 'best_ric_model.pt')
        torch.save(checkpoint, best_ric_model_path)
        print(f"已保存最佳RIC模型至 {best_ric_model_path}")

    # 保存最佳IC模型
    if is_best_ic:
        best_ic_model_path = os.path.join(checkpoint_dir, 'best_ic_model.pt')
        torch.save(checkpoint, best_ic_model_path)
        print(f"已保存最佳IC模型至 {best_ic_model_path}")


def train(args):
    """
    训练主函数
    
    参数:
        args: 命令行参数
    """
    # 设置随机种子
    setup_seed(args.seed)
    
    # 生成运行ID
    run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(f"运行ID: {run_id}")
    
    # 创建输出目录
    run_dir = os.path.join(config.OUTPUT_DIR, run_id)
    os.makedirs(run_dir, exist_ok=True)
    
    # 保存配置
    config_dict = {
        'factors': args.factors if args.factors else config.DEFAULT_FACTORS,
        'data_path': args.data_path,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.lr,
        'weight_decay': args.weight_decay,
        'save_interval': args.save_interval,
        'seed': args.seed,
        'scheduler': args.scheduler,
        'min_lr': args.min_lr,
        't_max': args.t_max if args.t_max else args.epochs,
        't_0': args.t_0,
        't_mult': args.t_mult,
        'lookback_window': config.LOOKBACK_WINDOW,
        'd_model': config.D_MODEL,
        'n_layer': config.N_LAYER,
        'num_heads': config.NUM_HEADS,
        'gnn_k': config.GNN_K,
        'node_embedding_dim': config.NODE_EMBEDDING_DIM,
        'cnn_blocks': config.CNN_BLOCKS,
        'cnn_kernel_sizes': config.CNN_KERNEL_SIZES,
        'cnn_bottleneck_scale': config.CNN_BOTTLENECK_SCALE,
        'dropout': config.DROPOUT,
        'run_id': run_id
    }

    with open(os.path.join(run_dir, 'config.json'), 'w') as f:
        json.dump(config_dict, f, indent=4)
    
    print("配置信息:")
    for k, v in config_dict.items():
        print(f"  {k}: {v}")
    
    # 加载并处理数据
    print("准备数据...")
    scaler_save_path = os.path.join(run_dir, 'feature_scaler.pkl')
    data_processor = DataProcessor(
        data_path=args.data_path,
        factors=args.factors,
        lookback=config.LOOKBACK_WINDOW,
        batch_size=args.batch_size
    )
    data_processor.scaler_save_path = scaler_save_path
    # 加载数据
    df = data_processor.load_data()
    # 按日期过滤
    if args.start_date:
        df = df[df['trade_date'] >= pd.to_datetime(args.start_date)]
    if args.end_date:
        df = df[df['trade_date'] <= pd.to_datetime(args.end_date)]
    dataloaders, data_info = data_processor.prepare_data(df)
    
    # 保存特征名称和标准化器，用于后续评估和增量更新
    with open(os.path.join(run_dir, 'data_info.pkl'), 'wb') as f:
        pickle.dump(data_info, f)
    
    # 初始化模型
    print("初始化模型...")
    model = SAMBAModel(
        input_dim=len(data_info['feature_names']),
        d_model=config.D_MODEL,
        n_layer=config.N_LAYER,
        num_heads=config.NUM_HEADS,
        gnn_k=config.GNN_K,
        node_embedding_dim=config.NODE_EMBEDDING_DIM,
        cnn_blocks=config.CNN_BLOCKS,
        cnn_kernel_sizes=config.CNN_KERNEL_SIZES,
        cnn_bottleneck_scale= config.CNN_BOTTLENECK_SCALE,
        dropout=config.DROPOUT
    ).to(config.DEVICE)
    
    # 打印模型结构
    print(model)
    
    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.AdamW(
        model.parameters(), 
        lr=args.lr,
        weight_decay=args.weight_decay
    )

    # 创建学习率调度器
    scheduler, need_step_per_epoch = create_scheduler(optimizer, args.scheduler, args)
    print(f"使用学习率调度器: {args.scheduler}")
    if args.scheduler == 'cosine':
        t_max = args.t_max if args.t_max else args.epochs
        print(f"余弦退火参数: T_max={t_max}, eta_min={args.min_lr}")
    elif args.scheduler == 'cosine_warm':
        print(f"热重启余弦退火参数: T_0={args.t_0}, T_mult={args.t_mult}, eta_min={args.min_lr}")
    
    
    # 初始化训练变量
    start_epoch = 0
    best_val_loss = float('inf')
    best_ric = float('-inf')  # 初始化最佳RIC值为负无穷
    best_ic = float('-inf')   # 初始化最佳IC值为负无穷
    train_losses = []
    val_losses = []
    val_metrics_history = []
    early_stop_counter = 0
    
    
    # 训练循环
    print(f"开始训练，共 {args.epochs} 轮...")
    for epoch in range(start_epoch, args.epochs):
        print(f"\n第 {epoch+1}/{args.epochs} 轮:")
        
        # 训练一个epoch
        train_loss = train_epoch(
            model, dataloaders['train'], criterion, optimizer, config.DEVICE
        )
        train_losses.append(train_loss)
        
        # 在验证集上评估
        val_loss, val_metrics = validate(
            model, dataloaders['val'], criterion, config.DEVICE
        )

        # 根据调度器类型调用step()
        if need_step_per_epoch:
            scheduler.step()  # 余弦退火调度器每个epoch调用
        else:
            scheduler.step(val_loss)  # ReduceLROnPlateau需要传入metric

        val_losses.append(val_loss)
        val_metrics_history.append(val_metrics)

        # 打印当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        print(f"当前学习率: {current_lr:.8f}")
        
        # 打印当前结果
        print(f"训练损失: {train_loss:.6f}, 验证损失: {val_loss:.6f}")
        print(f"验证指标: RMSE={val_metrics['rmse']:.6f}, R²={val_metrics['r2']:.6f}, IC={val_metrics['ic']:.6f}, RIC={val_metrics['ric']:.6f}")
        
        # 检查是否为最佳损失模型
        is_best = val_loss < best_val_loss
        if is_best:
            best_val_loss = val_loss
            early_stop_counter = 0
            print("发现新的最佳损失模型!")
        else:
            early_stop_counter += 1
            print(f"早停计数: {early_stop_counter}/{config.EARLY_STOP_PATIENCE}")
        
        # 检查是否为最佳RIC模型
        is_best_ric = val_metrics['ric'] > best_ric
        if is_best_ric:
            best_ric = val_metrics['ric']
            print("发现新的最佳RIC模型!")

        # 检查是否为最佳IC模型
        is_best_ic = val_metrics['ic'] > best_ic
        if is_best_ic:
            best_ic = val_metrics['ic']
            print("发现新的最佳IC模型!")

        # 保存检查点
        save_checkpoint(
            model, optimizer, epoch, val_metrics,
            best_val_loss, best_ric, best_ic, is_best, is_best_ric, is_best_ic, run_id
        )
        
        # 判断是否早停
        if early_stop_counter >= config.EARLY_STOP_PATIENCE:
            print(f"验证损失 {config.EARLY_STOP_PATIENCE} 轮未改善，提前停止训练")
            
            # 保存最后一轮的模型
            last_checkpoint_path = os.path.join(run_dir, 'last_model.pt')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_metrics': val_metrics,
                'best_val_loss': best_val_loss,
                'best_ric': best_ric,
                'best_ic': best_ic
            }, last_checkpoint_path)
            print(f"已保存最后一轮模型至 {last_checkpoint_path}")
            
            break
    
    # 训练结束后绘制并保存训练曲线
    plot_training_curves(
        train_losses, val_losses, val_metrics_history,
        save_path=os.path.join(run_dir, 'final_training_curves.png')
    )
    print(f"已保存最终训练曲线图到 {os.path.join(run_dir, 'final_training_curves.png')}")
    
    # 加载最佳损失模型进行测试
    best_model_path = os.path.join(run_dir, 'best_model.pt')
    checkpoint = torch.load(best_model_path, map_location=config.DEVICE, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 在测试集上评估最佳损失模型
    print("\n在测试集上评估最佳损失模型...")
    test_loss, test_metrics = validate(
        model, dataloaders['test'], criterion, config.DEVICE
    )
    print(f"测试损失: {test_loss:.6f}")
    print(f"测试指标: RMSE={test_metrics['rmse']:.6f}, R²={test_metrics['r2']:.6f}, IC={test_metrics['ic']:.6f}, RIC={test_metrics['ric']:.6f}")
    
    # 加载最佳RIC模型进行测试
    best_ric_model_path = os.path.join(run_dir, 'best_ric_model.pt')
    checkpoint_ric = torch.load(best_ric_model_path, map_location=config.DEVICE, weights_only=False)
    model.load_state_dict(checkpoint_ric['model_state_dict'])

    # 在测试集上评估最佳RIC模型
    print("\n在测试集上评估最佳RIC模型...")
    test_loss_ric, test_metrics_ric = validate(
        model, dataloaders['test'], criterion, config.DEVICE
    )
    print(f"测试损失: {test_loss_ric:.6f}")
    print(f"测试指标: RMSE={test_metrics_ric['rmse']:.6f}, R²={test_metrics_ric['r2']:.6f}, IC={test_metrics_ric['ic']:.6f}, RIC={test_metrics_ric['ric']:.6f}")

    # 加载最佳IC模型进行测试
    best_ic_model_path = os.path.join(run_dir, 'best_ic_model.pt')
    checkpoint_ic = torch.load(best_ic_model_path, map_location=config.DEVICE, weights_only=False)
    model.load_state_dict(checkpoint_ic['model_state_dict'])

    # 在测试集上评估最佳IC模型
    print("\n在测试集上评估最佳IC模型...")
    test_loss_ic, test_metrics_ic = validate(
        model, dataloaders['test'], criterion, config.DEVICE
    )
    print(f"测试损失: {test_loss_ic:.6f}")
    print(f"测试指标: RMSE={test_metrics_ic['rmse']:.6f}, R²={test_metrics_ic['r2']:.6f}, IC={test_metrics_ic['ic']:.6f}, RIC={test_metrics_ic['ric']:.6f}")
    
    # 保存测试结果
    results = {
        'test_loss': test_loss,
        'test_metrics': test_metrics,
        'test_loss_ric': test_loss_ric,
        'test_metrics_ric': test_metrics_ric,
        'test_loss_ic': test_loss_ic,
        'test_metrics_ic': test_metrics_ic,
        'train_history': {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_metrics': val_metrics_history
        },
        'epochs_completed': len(train_losses),
        'best_loss_epoch': checkpoint['epoch'],
        'best_val_loss': best_val_loss,
        'best_val_metrics': checkpoint['val_metrics'],
        'best_ric_epoch': checkpoint_ric['epoch'],
        'best_ric': best_ric,
        'best_ric_val_metrics': checkpoint_ric['val_metrics'],
        'best_ic_epoch': checkpoint_ic['epoch'],
        'best_ic': best_ic,
        'best_ic_val_metrics': checkpoint_ic['val_metrics']
    }
    
    with open(os.path.join(run_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=4, cls=NumpyEncoder)

    return run_id

class NumpyEncoder(json.JSONEncoder):
    """处理NumPy类型的JSON编码器"""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

if __name__ == "__main__":
    args = parse_args()
    train(args)
