<svg viewBox="0 0 1400 1800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义阴影效果 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="2" dy="2" result="offsetblur"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.2"/>
      </feComponentTransfer>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 定义箭头 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1400" height="1800" fill="#f8f9fa"/>
  
  
  <!-- 输入层 -->
  <g id="input">
    <rect x="550" y="80" width="300" height="60" rx="10" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" filter="url(#shadow)"/>
    <text x="700" y="115" text-anchor="middle" font-size="16" font-weight="bold" fill="#1976d2">输入数据</text>
    <text x="700" y="135" text-anchor="middle" font-size="12" fill="#666">[batch_size, seq_len, feature_dim]</text>
  </g>
  
  <!-- 特征注意力模块 -->
  <g id="feature-attention">
    <rect x="550" y="180" width="300" height="80" rx="10" fill="#fff3e0" stroke="#f57c00" stroke-width="2" filter="url(#shadow)"/>
    <text x="700" y="210" text-anchor="middle" font-size="16" font-weight="bold" fill="#f57c00">特征注意力模块</text>
    <text x="700" y="230" text-anchor="middle" font-size="12" fill="#666">动态选择重要特征</text>
    <text x="700" y="248" text-anchor="middle" font-size="11" fill="#888">门控注意力权重</text>
  </g>
  
  <!-- 连接线 -->
  <line x1="700" y1="140" x2="700" y2="180" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 三个分支的起始点 -->
  <circle cx="700" cy="300" r="8" fill="#666"/>
  <line x1="700" y1="260" x2="700" y2="300" stroke="#333" stroke-width="2"/>
  
  <!-- 左分支：CNN + Transformer -->
  <g id="left-branch">
    <!-- 连接线 -->
    <line x1="700" y1="300" x2="350" y2="340" stroke="#333" stroke-width="2"/>
    <line x1="350" y1="340" x2="350" y2="380" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- ComplexCNNFeatureExtractor -->
    <rect x="200" y="380" width="300" height="160" rx="10" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2" filter="url(#shadow)"/>
    <text x="350" y="410" text-anchor="middle" font-size="16" font-weight="bold" fill="#7b1fa2">复杂CNN特征提取器</text>
    
    <!-- Inception块 -->
    <g transform="translate(220, 430)">
      <rect x="0" y="0" width="260" height="90" rx="5" fill="#fff" stroke="#7b1fa2" stroke-width="1"/>
      <text x="130" y="20" text-anchor="middle" font-size="14" fill="#7b1fa2">Inception块 x N</text>
      <rect x="10" y="30" width="50" height="25" fill="#e1bee7" stroke="#7b1fa2"/>
      <text x="35" y="47" text-anchor="middle" font-size="10">1x1卷积</text>
      <rect x="70" y="30" width="50" height="25" fill="#e1bee7" stroke="#7b1fa2"/>
      <text x="95" y="47" text-anchor="middle" font-size="10">3x3卷积</text>
      <rect x="130" y="30" width="50" height="25" fill="#e1bee7" stroke="#7b1fa2"/>
      <text x="155" y="47" text-anchor="middle" font-size="10">5x5卷积</text>
      <rect x="190" y="30" width="60" height="25" fill="#e1bee7" stroke="#7b1fa2"/>
      <text x="220" y="47" text-anchor="middle" font-size="10">MaxPool</text>
      <text x="130" y="75" text-anchor="middle" font-size="11" fill="#666">多尺度特征提取</text>
    </g>
    
    <!-- Enhanced Transformer Blocks -->
    <rect x="200" y="580" width="300" height="280" rx="10" fill="#e8f5e9" stroke="#388e3c" stroke-width="2" filter="url(#shadow)"/>
    <text x="350" y="610" text-anchor="middle" font-size="16" font-weight="bold" fill="#388e3c">增强Transformer块 x N</text>
    
    <!-- RoPE -->
    <g transform="translate(220, 630)">
      <rect x="0" y="0" width="260" height="50" rx="5" fill="#c8e6c9" stroke="#388e3c"/>
      <text x="130" y="25" text-anchor="middle" font-size="14" font-weight="bold">RoPE</text>
      <text x="130" y="42" text-anchor="middle" font-size="11" fill="#666">旋转位置编码</text>
    </g>
    
    <!-- Multi-Head Attention -->
    <g transform="translate(220, 690)">
      <rect x="0" y="0" width="260" height="50" rx="5" fill="#c8e6c9" stroke="#388e3c"/>
      <text x="130" y="25" text-anchor="middle" font-size="14" font-weight="bold">多头自注意力</text>
      <text x="130" y="42" text-anchor="middle" font-size="11" fill="#666">Q, K, V 投影</text>
    </g>
    
    <!-- SwiGLU FFN -->
    <g transform="translate(220, 750)">
      <rect x="0" y="0" width="260" height="50" rx="5" fill="#c8e6c9" stroke="#388e3c"/>
      <text x="130" y="25" text-anchor="middle" font-size="14" font-weight="bold">SwiGLU FFN</text>
      <text x="130" y="42" text-anchor="middle" font-size="11" fill="#666">Swish门控线性单元</text>
    </g>
    
    <!-- RMSNorm -->
    <g transform="translate(220, 810)">
      <rect x="0" y="0" width="260" height="40" rx="5" fill="#c8e6c9" stroke="#388e3c"/>
      <text x="130" y="25" text-anchor="middle" font-size="12">RMS归一化 + 残差连接</text>
    </g>
    
    <!-- Temporal Head -->
    <rect x="200" y="900" width="300" height="80" rx="10" fill="#ffebee" stroke="#c62828" stroke-width="2" filter="url(#shadow)"/>
    <text x="350" y="930" text-anchor="middle" font-size="16" font-weight="bold" fill="#c62828">时序预测头</text>
    <text x="350" y="950" text-anchor="middle" font-size="12" fill="#666">LayerNorm → Linear → GELU</text>
    <text x="350" y="968" text-anchor="middle" font-size="12" fill="#666">→ Dropout → Linear</text>
    
    <!-- 连接线 -->
    <line x1="350" y1="540" x2="350" y2="580" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="350" y1="860" x2="350" y2="900" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="350" y1="980" x2="350" y2="1020" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- 输出标签 -->
    <text x="350" y="1040" text-anchor="middle" font-size="14" fill="#c62828">时序输出</text>
  </g>
  
  <!-- 中间分支：Factor Interaction -->
  <g id="middle-branch">
    <!-- 连接线 -->
    <line x1="700" y1="300" x2="700" y2="380" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- Factor Interaction Block -->
    <rect x="550" y="380" width="300" height="200" rx="10" fill="#fce4ec" stroke="#c2185b" stroke-width="2" filter="url(#shadow)"/>
    <text x="700" y="410" text-anchor="middle" font-size="16" font-weight="bold" fill="#c2185b">因子交互块</text>
    
    <!-- 内部组件 -->
    <g transform="translate(570, 430)">
      <rect x="0" y="0" width="260" height="40" rx="5" fill="#f8bbd0" stroke="#c2185b"/>
      <text x="130" y="25" text-anchor="middle" font-size="12">因子投影 (Linear)</text>
    </g>
    
    <g transform="translate(570, 480)">
      <rect x="0" y="0" width="260" height="40" rx="5" fill="#f8bbd0" stroke="#c2185b"/>
      <text x="130" y="25" text-anchor="middle" font-size="12">时间步注意力</text>
    </g>
    
    <g transform="translate(570, 530)">
      <rect x="0" y="0" width="260" height="40" rx="5" fill="#f8bbd0" stroke="#c2185b"/>
      <text x="130" y="25" text-anchor="middle" font-size="12">因子交互网络 (MLP)</text>
    </g>
    
    <!-- 连接线 -->
    <line x1="700" y1="580" x2="700" y2="620" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- 输出标签 -->
    <text x="700" y="640" text-anchor="middle" font-size="14" fill="#c2185b">因子输出</text>
  </g>
  
  <!-- 右分支：Adaptive GCN -->
  <g id="right-branch">
    <!-- 连接线 -->
    <line x1="700" y1="300" x2="1050" y2="340" stroke="#333" stroke-width="2"/>
    <line x1="1050" y1="340" x2="1050" y2="380" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- Adaptive GCN Block -->
    <rect x="900" y="380" width="300" height="320" rx="10" fill="#e0f2f1" stroke="#00695c" stroke-width="2" filter="url(#shadow)"/>
    <text x="1050" y="410" text-anchor="middle" font-size="16" font-weight="bold" fill="#00695c">自适应图卷积块</text>
    
    <!-- 内部组件 -->
    <g transform="translate(920, 430)">
      <rect x="0" y="0" width="260" height="50" rx="5" fill="#b2dfdb" stroke="#00695c"/>
      <text x="130" y="25" text-anchor="middle" font-size="12">节点嵌入</text>
      <text x="130" y="42" text-anchor="middle" font-size="11" fill="#666">[n_features, embedding_dim]</text>
    </g>
    
    <g transform="translate(920, 490)">
      <rect x="0" y="0" width="260" height="50" rx="5" fill="#b2dfdb" stroke="#00695c"/>
      <text x="130" y="25" text-anchor="middle" font-size="12">高斯核邻接矩阵</text>
      <text x="130" y="42" text-anchor="middle" font-size="11" fill="#666">A = exp(-ψ × D)</text>
    </g>
    
    <g transform="translate(920, 550)">
      <rect x="0" y="0" width="260" height="50" rx="5" fill="#b2dfdb" stroke="#00695c"/>
      <text x="130" y="25" text-anchor="middle" font-size="12">边缘注意力</text>
      <text x="130" y="42" text-anchor="middle" font-size="11" fill="#666">softmax(attention × temp)</text>
    </g>
    
    <g transform="translate(920, 610)">
      <rect x="0" y="0" width="260" height="70" rx="5" fill="#b2dfdb" stroke="#00695c"/>
      <text x="130" y="25" text-anchor="middle" font-size="12">切比雪夫多项式</text>
      <text x="130" y="42" text-anchor="middle" font-size="11" fill="#666">T₀, T₁, ..., Tₖ</text>
      <text x="130" y="60" text-anchor="middle" font-size="11" fill="#666">K阶多项式滤波器</text>
    </g>
    
    <!-- 连接线 -->
    <line x1="1050" y1="700" x2="1050" y2="740" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- 输出标签 -->
    <text x="1050" y="760" text-anchor="middle" font-size="14" fill="#00695c">图卷积输出</text>
  </g>
  
  <!-- 融合层 -->
  <g id="fusion">
    <!-- 汇聚点 -->
    <circle cx="700" cy="1200" r="8" fill="#666"/>
    
    <!-- 连接线 -->
    <line x1="350" y1="1050" x2="350" y2="1160" stroke="#333" stroke-width="2"/>
    <line x1="350" y1="1160" x2="700" y2="1200" stroke="#333" stroke-width="2"/>
    
    <line x1="700" y1="650" x2="700" y2="1200" stroke="#333" stroke-width="2"/>
    
    <line x1="1050" y1="770" x2="1050" y2="1160" stroke="#333" stroke-width="2"/>
    <line x1="1050" y1="1160" x2="700" y2="1200" stroke="#333" stroke-width="2"/>
    
    <!-- 加权融合 -->
    <rect x="550" y="1240" width="300" height="120" rx="10" fill="#fff8e1" stroke="#f9a825" stroke-width="2" filter="url(#shadow)"/>
    <text x="700" y="1270" text-anchor="middle" font-size="16" font-weight="bold" fill="#f9a825">加权融合</text>
    <text x="700" y="1295" text-anchor="middle" font-size="12" fill="#666">权重: [w₁, w₂, w₃]</text>
    <text x="700" y="1315" text-anchor="middle" font-size="12" fill="#666">softmax归一化</text>
    <text x="700" y="1340" text-anchor="middle" font-size="11" fill="#888">输出 = w₁×时序 + w₂×因子 + w₃×图</text>
    
    <!-- 连接线 -->
    <line x1="700" y1="1200" x2="700" y2="1240" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 最终输出 -->
  <g id="output">
    <rect x="550" y="1400" width="300" height="80" rx="10" fill="#e8f5e9" stroke="#2e7d32" stroke-width="3" filter="url(#shadow)"/>
    <text x="700" y="1435" text-anchor="middle" font-size="18" font-weight="bold" fill="#2e7d32">最终输出</text>
    <text x="700" y="1460" text-anchor="middle" font-size="14" fill="#666">[batch_size]</text>
    
    <!-- 连接线 -->
    <line x1="700" y1="1360" x2="700" y2="1400" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- 图例 -->
  <g id="legend" transform="translate(50, 1550)">
    <text x="0" y="0" font-size="14" font-weight="bold" fill="#333">图例：</text>
    <rect x="0" y="10" width="30" height="20" fill="#e3f2fd" stroke="#1976d2" stroke-width="2"/>
    <text x="40" y="25" font-size="12" fill="#666">输入/输出层</text>
    
    <rect x="150" y="10" width="30" height="20" fill="#fff3e0" stroke="#f57c00" stroke-width="2"/>
    <text x="190" y="25" font-size="12" fill="#666">注意力模块</text>
    
    <rect x="300" y="10" width="30" height="20" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2"/>
    <text x="340" y="25" font-size="12" fill="#666">CNN模块</text>
    
    <rect x="450" y="10" width="30" height="20" fill="#e8f5e9" stroke="#388e3c" stroke-width="2"/>
    <text x="490" y="25" font-size="12" fill="#666">Transformer模块</text>
    
    <rect x="0" y="40" width="30" height="20" fill="#fce4ec" stroke="#c2185b" stroke-width="2"/>
    <text x="40" y="55" font-size="12" fill="#666">因子交互模块</text>
    
    <rect x="150" y="40" width="30" height="20" fill="#e0f2f1" stroke="#00695c" stroke-width="2"/>
    <text x="190" y="55" font-size="12" fill="#666">图神经网络模块</text>
    
    <rect x="300" y="40" width="30" height="20" fill="#fff8e1" stroke="#f9a825" stroke-width="2"/>
    <text x="340" y="55" font-size="12" fill="#666">融合模块</text>
  </g>
  
  <!-- 参数标注 -->
  <g id="params" transform="translate(1100, 1550)">
    <text x="0" y="0" font-size="14" font-weight="bold" fill="#333">主要参数：</text>
    <text x="0" y="20" font-size="11" fill="#666">• d_model: 模型维度</text>
    <text x="0" y="40" font-size="11" fill="#666">• n_layer: Transformer层数</text>
    <text x="0" y="60" font-size="11" fill="#666">• num_heads: 注意力头数</text>
    <text x="0" y="80" font-size="11" fill="#666">• gnn_k: 切比雪夫多项式阶数</text>
  </g>
</svg>