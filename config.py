import os
import torch

# 数据配置
DATA_PATH = "tushare_data_cyb/stock_factors_cyb_train.csv"  # CSV文件路径

# 创业板股票因子列表
FACTORS = [
    'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount',
    'oversold_rsi_14', 'oversold_rsi_21', 'oversold_stoch_14', 'oversold_willr_14', 'oversold_cci_14', 'oversold_mfi_14', 'oversold_dmi', 'price_distance_ma_5', 'ema_distance_5', 'price_distance_ma_10',
    'ema_distance_10', 'price_distance_ma_20', 'ema_distance_20', 'price_distance_ma_60', 'ema_distance_60', 'price_distance_ma_120', 'ema_distance_120', 'multiple_ma_support', 'ma_convergence', 'bias_oversold_10',
    'bias_oversold_20', 'bias_oversold_60', 'volume_price_divergence_10', 'volume_price_divergence_20', 'capitulation_volume', 'washout_bottom', 'smart_money_accumulation', 'vpt_bottom', 'volume_reversal', 'hammer_pattern',
    'doji_pattern', 'bullish_engulfing', 'morning_star', 'three_white_soldiers', 'bullish_harami', 'long_legged_doji', 'support_resistance_level', 'fibonacci_support', 'mean_reversion_signal', 'parabolic_sar_bottom',
    'ichimoku_support', 'trend_channel_support', 'oversold_bounce_potential', 'insider_buying_signal', 'volatility_contraction', 'macd_golden_cross_oversold', 'kdj_oversold_cross', 'j_value_oversold', 'rsi_divergence_bottom', 'fear_greed_bottom',
    'sentiment_extreme_bottom', 'contrarian_signal', 'valuation_bottom', 'earnings_yield_bottom', 'multi_timeframe_oversold', 'momentum_reversal_combo', 'technical_confluence_bottom', 'bollinger_oversold_20', 'bollinger_oversold_10', 'technical_oversold_score',
    'pattern_oversold_score', 'volume_oversold_score', 'reversal_signal_score', 'final_bottom_fishing_score', 'risk_adjusted_bottom_score', 'bottom_fishing_timing', 'awesome_oscillator_oversold', 'ultimate_oscillator_oversold', 'trix_oversold', 'aroon_oversold',
    'chaikin_oscillator_oversold', 'atr_oversold_signal', 'vix_proxy_oversold', 'bollinger_bandwidth_oversold', 'keltner_channel_oversold', 'donchian_channel_oversold', 'adx_trend_weakness', 'dpo_oversold', 'linear_regression_deviation', 'vortex_oversold',
    'mass_index_oversold', 'ease_of_movement_oversold', 'force_index_oversold', 'accumulation_swing_index', 'fear_greed_enhanced', 'oversold_momentum_divergence', 'washout_reversal_combo', 'support_convergence', 'volatility_breakout_reversal', 'smart_money_distribution',
    'institutional_footprint'
]


DEFAULT_FACTORS = [
    'pct_chg', 'dpo_oversold', 'amount', 'accumulation_swing_index',
    'volatility_contraction', 'vortex_oversold', 'trend_channel_support', 'j_value_oversold',
    'price_distance_ma_120', 'ichimoku_support', 'trix_oversold', 'linear_regression_deviation',
    'price_distance_ma_5', 'ema_distance_120', 'technical_oversold_score', 'aroon_oversold',
    'ease_of_movement_oversold', 'volume_price_divergence_10', 'price_distance_ma_60', 'vix_proxy_oversold',
    'oversold_dmi', 'risk_adjusted_bottom_score', 'oversold_cci_14', 'fibonacci_support',
    'price_distance_ma_10', 'fear_greed_enhanced', 'volume_price_divergence_20', 'vol',
    'awesome_oscillator_oversold', 'valuation_bottom', 'mass_index_oversold', 'chaikin_oscillator_oversold',
    'ema_distance_60', 'ema_distance_20', 'bollinger_oversold_10', 'contrarian_signal',
    'final_bottom_fishing_score', 'force_index_oversold', 'open', 'oversold_stoch_14',
    'ema_distance_5', 'bias_oversold_60', 'price_distance_ma_20', 'volume_oversold_score',
    'ultimate_oscillator_oversold', 'close', 'low', 'support_resistance_level',
    'bollinger_bandwidth_oversold', 'ema_distance_10', 'oversold_rsi_14', 'parabolic_sar_bottom',
    'donchian_channel_oversold', 'high', 'washout_bottom', 'adx_trend_weakness',
    'bias_oversold_20', 'keltner_channel_oversold', 'multiple_ma_support', 'bias_oversold_10',
    'oversold_willr_14', 'bollinger_oversold_20', 'ma_convergence', 'mean_reversion_signal',
    'earnings_yield_bottom', 'smart_money_distribution', 'oversold_rsi_21', 'oversold_mfi_14',
    'fear_greed_bottom', 'capitulation_volume', 'technical_confluence_bottom', 'multi_timeframe_oversold',
    'three_white_soldiers', 'hammer_pattern', 'rsi_divergence_bottom', 'oversold_momentum_divergence',
    'kdj_oversold_cross', 'reversal_signal_score', 'atr_oversold_signal', 'bullish_engulfing',
    'vpt_bottom', 'doji_pattern', 'pattern_oversold_score', 'smart_money_accumulation',
    'long_legged_doji', 'support_convergence', 'bottom_fishing_timing', 'oversold_bounce_potential',
    'institutional_footprint', 'morning_star', 'bullish_harami', 'macd_golden_cross_oversold',
    'momentum_reversal_combo', 'sentiment_extreme_bottom', 'insider_buying_signal', 'volatility_breakout_reversal',
]



# 目标变量
TARGET = "target_return"

# 模型配置 - 无平均操作版本，最大化敏感性
LOOKBACK_WINDOW = 10  # 历史数据窗口大小 - 进一步减少滞后
BATCH_SIZE = 64      # 批处理大小
D_MODEL = 64          # 模型维度
N_LAYER = 3           # Transformer层数 - 进一步减少过度平滑
NUM_HEADS = 4         # 多头注意力头数 - 减少计算复杂度
GNN_K = 2             # 图神经网络的多项式阶数 - 最小化平滑
NODE_EMBEDDING_DIM = 16  # 节点嵌入维度 - 减少参数
CNN_BLOCKS = 1               # Inception块的数量 - 减少平滑
CNN_KERNEL_SIZES = [3] # 单一卷积核 - 避免多尺度平均
CNN_BOTTLENECK_SCALE = 0.5   # 瓶颈层通道缩放比例 (相对于d_model)
DROPOUT = 0.02        # 极低dropout，最大化信息保留

'''
LOOKBACK_WINDOW = 10  # 历史数据窗口大小
BATCH_SIZE = 64      # 批处理大小
D_MODEL = 64          # 模型维度
N_LAYER = 6           # Transformer层数
NUM_HEADS = 8         # 多头注意力头数
GNN_K = 4             # 图神经网络的多项式阶数
NODE_EMBEDDING_DIM = 32  # 节点嵌入维度
CNN_BLOCKS = 2               # Inception块的数量
CNN_KERNEL_SIZES = [3, 5, 7] # 多尺度卷积核大小列表
CNN_BOTTLENECK_SCALE = 0.5   # 瓶颈层通道缩放比例 (相对于d_model)
DROPOUT = 0.1
'''


# 训练配置
EPOCHS = 300         # 训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-4   # 权重衰减
SAVE_INTERVAL = 10    # 权重保存间隔
EARLY_STOP_PATIENCE = 30  # 早停耐心

# 路径配置
OUTPUT_DIR = "output"  # 输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
